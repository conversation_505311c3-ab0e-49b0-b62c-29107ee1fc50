<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\VotingCenter;
use App\Models\State;
use App\Models\Municipality;
use App\Models\Parish;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class ElectoralApiController extends Controller
{
    /**
     * Get electoral information by citizen ID
     */
    public function getCitizenInfo(Request $request, string $cedula): JsonResponse
    {
        $request->validate([
            'include_center' => 'nullable|boolean',
            'include_location' => 'nullable|boolean',
        ]);

        // Simular búsqueda de información electoral
        // En un sistema real, esto consultaría la base de datos del CNE
        $votingCenter = $this->findVotingCenterByCedula($cedula);

        if (!$votingCenter) {
            return response()->json([
                'success' => false,
                'message' => 'Ciudadano no encontrado en el registro electoral',
                'cedula' => $cedula
            ], 404);
        }

        $response = [
            'success' => true,
            'data' => [
                'cedula' => $cedula,
                'electoral_status' => 'active',
                'table_number' => $this->generateTableNumber($votingCenter),
                'voting_center_code' => $votingCenter->code,
            ],
            'message' => 'Información electoral encontrada'
        ];

        // Incluir información del centro si se solicita
        if ($request->get('include_center', true)) {
            $response['data']['voting_center'] = $votingCenter;
        }

        // Incluir información de ubicación si se solicita
        if ($request->get('include_location', true)) {
            $response['data']['location'] = [
                'state' => $votingCenter->state->name,
                'municipality' => $votingCenter->municipality->name,
                'parish' => $votingCenter->parish->name,
            ];
        }

        return response()->json($response);
    }

    /**
     * Get voting centers by geographic area
     */
    public function getCentersByArea(Request $request): JsonResponse
    {
        $request->validate([
            'state_id' => 'nullable|exists:states,id',
            'municipality_id' => 'nullable|exists:municipalities,id',
            'parish_id' => 'nullable|exists:parishes,id',
            'include_stats' => 'nullable|boolean',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = VotingCenter::with(['state', 'municipality', 'parish']);

        // Aplicar filtros
        if ($request->filled('state_id')) {
            $query->where('state_id', $request->state_id);
        }

        if ($request->filled('municipality_id')) {
            $query->where('municipality_id', $request->municipality_id);
        }

        if ($request->filled('parish_id')) {
            $query->where('parish_id', $request->parish_id);
        }

        $query->where('status', 'active')->orderBy('name');

        $perPage = $request->get('per_page', 20);
        $centers = $query->paginate($perPage);

        $response = [
            'success' => true,
            'data' => $centers,
            'message' => 'Centros de votación obtenidos exitosamente'
        ];

        // Incluir estadísticas si se solicita
        if ($request->get('include_stats', false)) {
            $response['statistics'] = [
                'total_centers' => $centers->total(),
                'total_voters' => $query->sum('total_voters'),
                'total_tables' => $query->sum('total_tables'),
                'average_voters_per_center' => round($query->avg('total_voters'), 0),
            ];
        }

        return response()->json($response);
    }

    /**
     * Search voting centers with advanced filters
     */
    public function searchCenters(Request $request): JsonResponse
    {
        $request->validate([
            'q' => 'nullable|string|max:255',
            'code' => 'nullable|string|max:20',
            'state' => 'nullable|string|max:100',
            'municipality' => 'nullable|string|max:100',
            'parish' => 'nullable|string|max:100',
            'min_voters' => 'nullable|integer|min:0',
            'max_voters' => 'nullable|integer|min:0',
            'status' => 'nullable|in:active,inactive,suspended',
            'has_coordinates' => 'nullable|boolean',
            'sort_by' => 'nullable|in:name,code,total_voters,total_tables',
            'sort_order' => 'nullable|in:asc,desc',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = VotingCenter::with(['state', 'municipality', 'parish']);

        // Búsqueda por texto general
        if ($request->filled('q')) {
            $search = $request->q;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('code', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%");
            });
        }

        // Búsqueda por código específico
        if ($request->filled('code')) {
            $query->where('code', 'like', "%{$request->code}%");
        }

        // Búsqueda por ubicación
        if ($request->filled('state')) {
            $query->whereHas('state', function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->state}%");
            });
        }

        if ($request->filled('municipality')) {
            $query->whereHas('municipality', function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->municipality}%");
            });
        }

        if ($request->filled('parish')) {
            $query->whereHas('parish', function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->parish}%");
            });
        }

        // Filtros numéricos
        if ($request->filled('min_voters')) {
            $query->where('total_voters', '>=', $request->min_voters);
        }

        if ($request->filled('max_voters')) {
            $query->where('total_voters', '<=', $request->max_voters);
        }

        // Filtro de status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filtro de coordenadas
        if ($request->filled('has_coordinates')) {
            if ($request->has_coordinates) {
                $query->whereNotNull('latitude')->whereNotNull('longitude');
            } else {
                $query->where(function ($q) {
                    $q->whereNull('latitude')->orWhereNull('longitude');
                });
            }
        }

        // Ordenamiento
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginación
        $perPage = $request->get('per_page', 20);
        $centers = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $centers,
            'search_params' => $request->only([
                'q', 'code', 'state', 'municipality', 'parish',
                'min_voters', 'max_voters', 'status', 'has_coordinates'
            ]),
            'message' => 'Búsqueda completada exitosamente'
        ]);
    }

    /**
     * Get electoral statistics
     */
    public function getElectoralStats(Request $request): JsonResponse
    {
        $cacheKey = 'electoral_stats_' . md5($request->getQueryString());
        
        $stats = Cache::remember($cacheKey, 300, function () use ($request) { // Cache por 5 minutos
            $baseQuery = VotingCenter::query();

            // Aplicar filtros si se proporcionan
            if ($request->filled('state_id')) {
                $baseQuery->where('state_id', $request->state_id);
            }

            return [
                'general' => [
                    'total_centers' => $baseQuery->count(),
                    'active_centers' => $baseQuery->where('status', 'active')->count(),
                    'total_voters' => $baseQuery->sum('total_voters'),
                    'total_tables' => $baseQuery->sum('total_tables'),
                    'average_voters_per_center' => round($baseQuery->avg('total_voters'), 0),
                    'average_tables_per_center' => round($baseQuery->avg('total_tables'), 1),
                ],
                'by_state' => $this->getStatsByState($baseQuery),
                'by_status' => $this->getStatsByStatus($baseQuery),
                'distribution' => $this->getVoterDistribution($baseQuery),
                'coordinates_coverage' => $this->getCoordinatesCoverage($baseQuery),
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $stats,
            'generated_at' => now(),
            'message' => 'Estadísticas electorales obtenidas exitosamente'
        ]);
    }

    /**
     * Get nearby voting centers using coordinates
     */
    public function getNearbyVotingCenters(Request $request): JsonResponse
    {
        $request->validate([
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
            'radius' => 'nullable|numeric|min:0.1|max:100',
            'limit' => 'nullable|integer|min:1|max:50',
            'include_distance' => 'nullable|boolean',
        ]);

        $lat = $request->latitude;
        $lng = $request->longitude;
        $radius = $request->get('radius', 10); // 10km por defecto
        $limit = $request->get('limit', 15);

        $centers = VotingCenter::with(['state', 'municipality', 'parish'])
            ->whereNotNull('latitude')
            ->whereNotNull('longitude')
            ->selectRaw(
                "*, (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * cos(radians(longitude) - radians(?)) + sin(radians(?)) * sin(radians(latitude)))) AS distance",
                [$lat, $lng, $lat]
            )
            ->having('distance', '<=', $radius)
            ->orderBy('distance')
            ->limit($limit)
            ->get();

        $response = [
            'success' => true,
            'data' => $centers,
            'search_params' => [
                'latitude' => $lat,
                'longitude' => $lng,
                'radius_km' => $radius,
                'limit' => $limit,
            ],
            'message' => "Se encontraron {$centers->count()} centros en un radio de {$radius}km"
        ];

        // Incluir información de distancia si se solicita
        if ($request->get('include_distance', true)) {
            $response['data'] = $centers->map(function ($center) {
                $center->distance_km = round($center->distance, 2);
                return $center;
            });
        }

        return response()->json($response);
    }

    /**
     * Simulate finding voting center by citizen ID
     */
    private function findVotingCenterByCedula(string $cedula): ?VotingCenter
    {
        // En un sistema real, esto consultaría la base de datos del CNE
        // Por ahora, simulamos usando el último dígito de la cédula
        $lastDigit = (int) substr($cedula, -1);
        
        return VotingCenter::with(['state', 'municipality', 'parish'])
            ->where('status', 'active')
            ->skip($lastDigit * 100)
            ->first();
    }

    /**
     * Generate table number for a citizen
     */
    private function generateTableNumber(VotingCenter $center): int
    {
        return rand(1, max(1, $center->total_tables));
    }

    /**
     * Get statistics by state
     */
    private function getStatsByState($baseQuery)
    {
        return $baseQuery->join('states', 'voting_centers.state_id', '=', 'states.id')
            ->select('states.name as state_name')
            ->selectRaw('COUNT(*) as total_centers')
            ->selectRaw('SUM(total_voters) as total_voters')
            ->selectRaw('SUM(total_tables) as total_tables')
            ->groupBy('states.id', 'states.name')
            ->orderBy('total_centers', 'desc')
            ->get();
    }

    /**
     * Get statistics by status
     */
    private function getStatsByStatus($baseQuery)
    {
        return $baseQuery->select('status')
            ->selectRaw('COUNT(*) as count')
            ->selectRaw('SUM(total_voters) as total_voters')
            ->groupBy('status')
            ->get();
    }

    /**
     * Get voter distribution
     */
    private function getVoterDistribution($baseQuery)
    {
        return [
            'small_centers' => $baseQuery->where('total_voters', '<', 500)->count(),
            'medium_centers' => $baseQuery->whereBetween('total_voters', [500, 1500])->count(),
            'large_centers' => $baseQuery->where('total_voters', '>', 1500)->count(),
        ];
    }

    /**
     * Get coordinates coverage
     */
    private function getCoordinatesCoverage($baseQuery)
    {
        $total = $baseQuery->count();
        $withCoordinates = $baseQuery->whereNotNull('latitude')->whereNotNull('longitude')->count();
        
        return [
            'total_centers' => $total,
            'with_coordinates' => $withCoordinates,
            'without_coordinates' => $total - $withCoordinates,
            'coverage_percentage' => $total > 0 ? round(($withCoordinates / $total) * 100, 1) : 0,
        ];
    }
}
